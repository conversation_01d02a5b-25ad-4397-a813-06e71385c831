//@version=6
indicator("Unicorn ABC FVG Instant Signal", "ABC FVG Instant", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// Settings
swingLength = input.int(10, "Swing Length", minval=2, maxval=50, group="Pattern Detection")
showBull = input.bool(true, "Show Bullish Patterns", group="Pattern Display")
bullColor = input.color(#089981, "", group="Pattern Display")
showBear = input.bool(true, "Show Bearish Patterns", group="Pattern Display")
bearColor = input.color(#f23645, "", group="Pattern Display")
showTargets = input.bool(true, "Show Risk/Reward Areas", group="Risk Management")
riskAreaColor = input.color(color.new(#787b86, 80), "Risk Area", group="Risk Management")
rewardAreaColor = input.color(color.new(#5b9cf6, 80), "Reward Area", group="Risk Management")
riskRatio = input.float(1.0, "Risk", step=0.1, group="Risk Management")
rewardRatio = input.float(2.0, "Reward", step=0.1, group="Risk Management")
rrRatio = rewardRatio / riskRatio

// Types
var float na_f = na
var int na_i = na

type ZigZag
    array<int> directions
    array<int> barIndices
    array<float> prices

// Initialize ZigZag
var zigzag = ZigZag.new(array.new<int>(100, 0), array.new<int>(100, 0), array.new<float>(100, na))
var float maxPivot = 0.0
var float minPivot = 999999.0

// Helper function
updateZigZag(direction, barIdx, price) =>
    zigzag.directions.unshift(direction)
    zigzag.barIndices.unshift(barIdx)
    zigzag.prices.unshift(price)
    
    if zigzag.directions.size() > 100
        zigzag.directions.pop()
        zigzag.barIndices.pop()
        zigzag.prices.pop()

// Main Logic
currentBar = bar_index
atrValue = ta.atr(14)

// Detect pivot points
pivotHigh = ta.pivothigh(swingLength, 1)
pivotLow = ta.pivotlow(swingLength, 1)

if not na(pivotHigh)
    maxPivot := math.max(maxPivot, pivotHigh)
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection <= 0
        updateZigZag(1, currentBar - 1, pivotHigh)
    else
        if pivotHigh > zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotHigh)

if not na(pivotLow)
    minPivot := math.min(minPivot, pivotLow)
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection >= 0
        updateZigZag(-1, currentBar - 1, pivotLow)
    else
        if pivotLow < zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotLow)

// Pattern Detection (A-B-C only, instant FVG trigger)
var box fvgBox = na
var box riskBox = na
var box rewardBox = na
var bool patternActive = false
var float patternFvgTop = na
var float patternFvgBottom = na
var float patternStop = na
var float patternTarget = na
var bool isBullish = false

if zigzag.directions.size() >= 3 and zigzag.barIndices.size() >= 3 and zigzag.prices.size() >= 3
    pointC = zigzag.prices.get(0)
    pointB = zigzag.prices.get(1)
    pointA = zigzag.prices.get(2)
    barC = zigzag.barIndices.get(0)
    barB = zigzag.barIndices.get(1)
    barA = zigzag.barIndices.get(2)
    dirC = zigzag.directions.get(0)
    dirB = zigzag.directions.get(1)
    dirA = zigzag.directions.get(2)

    // Bullish: Low-High-Low (C < A)
    if dirA == -1 and dirB == 1 and dirC == -1 and pointC < pointA and showBull
        fvgFound = false
        fvgTop = na
        fvgBottom = na
        fvgBar = na
        searchStart = math.max(0, currentBar - barC)
        searchEnd = math.max(0, currentBar - barB)
        for i = searchStart to math.min(searchEnd, 50)
            if i + 2 < bar_index
                gapBottom = high[i + 2]
                gapTop = low[i]
                if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                    if gapBottom >= pointC and gapBottom <= pointB and gapTop >= pointC and gapTop <= pointB
                        fvgFound := true
                        fvgTop := gapTop
                        fvgBottom := gapBottom
                        fvgBar := currentBar - i - 2
                        break
        if fvgFound
            patternActive := true
            patternFvgTop := fvgTop
            patternFvgBottom := fvgBottom
            isBullish := true
            patternStop := pointC - atrValue * 0.5
            patternTarget := fvgTop + (fvgTop - patternStop) * rrRatio
            if not na(fvgBox)
                box.delete(fvgBox)
            fvgBox := box.new(fvgBar, fvgBottom, currentBar, fvgTop, bgcolor=color.new(bullColor, 75), border_color=color.new(bullColor, 50))

    // Bearish: High-Low-High (C > A)
    if dirA == 1 and dirB == -1 and dirC == 1 and pointC > pointA and showBear
        fvgFound = false
        fvgTop = na
        fvgBottom = na
        fvgBar = na
        searchStart = math.max(0, currentBar - barC)
        searchEnd = math.max(0, currentBar - barB)
        for i = searchStart to math.min(searchEnd, 50)
            if i + 2 < bar_index
                gapTop = low[i + 2]
                gapBottom = high[i]
                if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                    if gapTop <= pointC and gapTop >= pointB and gapBottom <= pointC and gapBottom >= pointB
                        fvgFound := true
                        fvgTop := gapTop
                        fvgBottom := gapBottom
                        fvgBar := currentBar - i - 2
                        break
        if fvgFound
            patternActive := true
            patternFvgTop := fvgTop
            patternFvgBottom := fvgBottom
            isBullish := false
            patternStop := pointC + atrValue * 0.5
            patternTarget := fvgBottom - (patternStop - fvgBottom) * rrRatio
            if not na(fvgBox)
                box.delete(fvgBox)
            fvgBox := box.new(fvgBar, fvgBottom, currentBar, fvgTop, bgcolor=color.new(bearColor, 75), border_color=color.new(bearColor, 50))

// Signal Triggering and Risk/Reward Boxes
if patternActive and not na(fvgBox)
    // Update FVG box
    box.set_right(fvgBox, currentBar)
    
    // For bullish: close above FVG top
    if isBullish and close > patternFvgTop and open < patternFvgTop
        line.new(bar_index, patternFvgTop, bar_index + 5, patternFvgTop, color=bullColor, width=3)
        label.new(bar_index, patternFvgTop, text="Bull Signal", color=color.new(bullColor, 0), style=label.style_label_up, textcolor=color.white)
        if showTargets
            riskBox := box.new(bar_index, patternStop, bar_index + 1, patternFvgTop, border_color=color.new(na, na), bgcolor=riskAreaColor)
            rewardBox := box.new(bar_index, patternFvgTop, bar_index + 1, patternTarget, border_color=color.new(na, na), bgcolor=rewardAreaColor)
        patternActive := false
    
    // For bearish: close below FVG bottom
    if not isBullish and close < patternFvgBottom and open > patternFvgBottom
        line.new(bar_index, patternFvgBottom, bar_index + 5, patternFvgBottom, color=bearColor, width=3)
        label.new(bar_index, patternFvgBottom, text="Bear Signal", color=color.new(bearColor, 0), style=label.style_label_down, textcolor=color.white)
        if showTargets
            riskBox := box.new(bar_index, patternFvgBottom, bar_index + 1, patternStop, border_color=color.new(na, na), bgcolor=riskAreaColor)
            rewardBox := box.new(bar_index, patternTarget, bar_index + 1, patternFvgBottom, border_color=color.new(na, na), bgcolor=rewardAreaColor)
        patternActive := false

// Update risk/reward boxes
if not na(riskBox)
    box.set_right(riskBox, currentBar)
if not na(rewardBox)
    box.set_right(rewardBox, currentBar)

// Alert conditions
bullSignal = patternActive and isBullish and close > patternFvgTop and open < patternFvgTop
bearSignal = patternActive and not isBullish and close < patternFvgBottom and open > patternFvgBottom

alertcondition(bullSignal, "Bullish ABC FVG Instant Signal", "Bullish ABC FVG Instant Signal Triggered")
alertcondition(bearSignal, "Bearish ABC FVG Instant Signal", "Bearish ABC FVG Instant Signal Triggered")
alertcondition(bullSignal or bearSignal, "Any ABC FVG Instant Signal", "ABC FVG Instant Signal Triggered")
