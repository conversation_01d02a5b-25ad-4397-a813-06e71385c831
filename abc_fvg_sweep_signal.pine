//@version=6
indicator("Unicorn ABC FVG Sweep Signal", "ABC FVG Sweep", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// Settings
swingLength = input.int(10, "Swing Length", minval=2, maxval=50, group="Pattern Detection")
showBull = input.bool(true, "Show Bullish Patterns", group="Pattern Display")
bullColor = input.color(#089981, "", group="Pattern Display")
showBear = input.bool(true, "Show Bearish Patterns", group="Pattern Display")
bearColor = input.color(#f23645, "", group="Pattern Display")

// Types
var float na_f = na
var int na_i = na

type ZigZag
    array<int> directions
    array<int> barIndices
    array<float> prices

// Variables
var zigzag = ZigZag.new(array.new<int>(100, 0), array.new<int>(100, 0), array.new<float>(100, na))

// Helper Functions
updateZigZag(direction, barIdx, price) =>
    zigzag.directions.unshift(direction)
    zigzag.barIndices.unshift(barIdx)
    zigzag.prices.unshift(price)
    if zigzag.directions.size() > 100
        zigzag.directions.pop()
        zigzag.barIndices.pop()
        zigzag.prices.pop()

// Main Logic
currentBar = bar_index
atrValue = ta.atr(14)

// Detect pivot points
pivotHigh = ta.pivothigh(swingLength, 1)
pivotLow = ta.pivotlow(swingLength, 1)

if not na(pivotHigh)
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection <= 0
        updateZigZag(1, currentBar - 1, pivotHigh)
    else
        if pivotHigh > zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotHigh)

if not na(pivotLow)
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection >= 0
        updateZigZag(-1, currentBar - 1, pivotLow)
    else
        if pivotLow < zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotLow)

// Pattern Detection (A-B-C only, with sweep and FVG logic)
var box fvgBox = na
var bool patternActive = false
var float patternFvgTop = na
var float patternFvgBottom = na
var bool isBullish = false
var bool sweepDetected = false
var float sweepLevel = na
var float fvgTop = na
var float fvgBottom = na
var int fvgBar = na

if zigzag.directions.size() >= 3 and zigzag.barIndices.size() >= 3 and zigzag.prices.size() >= 3
    pointC = zigzag.prices.get(0)
    pointB = zigzag.prices.get(1)
    pointA = zigzag.prices.get(2)
    barC = zigzag.barIndices.get(0)
    barB = zigzag.barIndices.get(1)
    barA = zigzag.barIndices.get(2)
    dirC = zigzag.directions.get(0)
    dirB = zigzag.directions.get(1)
    dirA = zigzag.directions.get(2)

    // Bullish: Low-High-Low (C < A)
    if dirA == -1 and dirB == 1 and dirC == -1 and pointC < pointA and showBull
        sweepLevel := math.min(pointA, pointC)
        // Wait for price to sweep the most recent swing low
        if low < sweepLevel and not sweepDetected
            sweepDetected := true
        // After sweep, look for bearish inversion FVG between B and C
        if sweepDetected
            fvgFound = false
            fvgTop = na
            fvgBottom = na
            fvgBar = na
            searchStart = math.max(0, currentBar - barC)
            searchEnd = math.max(0, currentBar - barB)
            for i = searchStart to math.min(searchEnd, 50)
                if i + 2 < bar_index
                    gapTop = low[i + 2]
                    gapBottom = high[i]
                    if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                        if gapTop <= pointC and gapTop >= pointB and gapBottom <= pointC and gapBottom >= pointB
                            fvgFound := true
                            fvgTop := gapTop
                            fvgBottom := gapBottom
                            fvgBar := currentBar - i - 2
                            break
            if fvgFound
                patternActive := true
                patternFvgTop := fvgTop
                patternFvgBottom := fvgBottom
                isBullish := true
                if not na(fvgBox)
                    box.delete(fvgBox)
                fvgBox := box.new(fvgBar, fvgBottom, currentBar, fvgTop, bgcolor=color.new(bullColor, 75), border_color=color.new(bullColor, 50))

    // Bearish: High-Low-High (C > A)
    if dirA == 1 and dirB == -1 and dirC == 1 and pointC > pointA and showBear
        sweepLevel := math.max(pointA, pointC)
        // Wait for price to sweep the most recent swing high
        if high > sweepLevel and not sweepDetected
            sweepDetected := true
        // After sweep, look for bullish inversion FVG between B and C
        if sweepDetected
            fvgFound = false
            fvgTop = na
            fvgBottom = na
            fvgBar = na
            searchStart = math.max(0, currentBar - barC)
            searchEnd = math.max(0, currentBar - barB)
            for i = searchStart to math.min(searchEnd, 50)
                if i + 2 < bar_index
                    gapBottom = high[i + 2]
                    gapTop = low[i]
                    if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                        if gapBottom >= pointC and gapBottom <= pointB and gapTop >= pointC and gapTop <= pointB
                            fvgFound := true
                            fvgTop := gapTop
                            fvgBottom := gapBottom
                            fvgBar := currentBar - i - 2
                            break
            if fvgFound
                patternActive := true
                patternFvgTop := fvgTop
                patternFvgBottom := fvgBottom
                isBullish := false
                if not na(fvgBox)
                    box.delete(fvgBox)
                fvgBox := box.new(fvgBar, fvgBottom, currentBar, fvgTop, bgcolor=color.new(bearColor, 75), border_color=color.new(bearColor, 50))

// Signal Triggering
if patternActive and not na(fvgBox)
    // For bullish: close above FVG top
    if isBullish and close > patternFvgTop and open < patternFvgTop
        label.new(bar_index, patternFvgTop, text="Bull Sweep Signal", color=color.new(bullColor, 0), style=label.style_label_up, textcolor=color.white)
        patternActive := false
        sweepDetected := false
    // For bearish: close below FVG bottom
    if not isBullish and close < patternFvgBottom and open > patternFvgBottom
        label.new(bar_index, patternFvgBottom, text="Bear Sweep Signal", color=color.new(bearColor, 0), style=label.style_label_down, textcolor=color.white)
        patternActive := false
        sweepDetected := false 