//@version=6
indicator("Apex Unicorn v6 - Early Detection", "Unicorn v6 ED - <PERSON>", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

//-----------------------------------------------------------------------------
// Settings
//-----------------------------------------------------------------------------
swingLength = input.int(10, "Swing Length", minval=2, maxval=50, group="Pattern Detection")

showBull = input.bool(true, "Show Bullish Patterns", inline="bull", group="Pattern Display")
bullColor = input.color(#089981, "", inline="bull", group="Pattern Display")

showBear = input.bool(true, "Show Bearish Patterns", inline="bear", group="Pattern Display")
bearColor = input.color(#f23645, "", inline="bear", group="Pattern Display")

combinePatterns = input.bool(false, "Allow Overlapping Patterns", tooltip="Allow bull and bear patterns simultaneously", group="Pattern Display")

// Early Entry Settings
enableEarlyEntry = input.bool(true, "Enable Early Entry Detection", group="Early Entry")
earlyEntryColor = input.color(#ff9800, "Early Entry Color", group="Early Entry")
showEarlyEntryLabels = input.bool(true, "Show Early Entry Labels", group="Early Entry")

// Risk/Reward Settings
riskRatio = input.float(1.0, "Risk", step=0.1, inline="rr", group="Risk Management")
rewardRatio = input.float(2.0, "Reward", step=0.1, inline="rr", group="Risk Management")
rrRatio = rewardRatio / riskRatio

enableTrailing = input.bool(false, "Enable Trailing Stop", group="Risk Management")
trailingLength = input.int(5, "Trailing Length", minval=3, maxval=10, group="Risk Management")

showTargets = input.bool(true, "Show Target Areas", inline="targets", group="Visual")
riskAreaColor = input.color(color.new(#787b86, 80), "Risk", inline="targets", group="Visual")
rewardAreaColor = input.color(color.new(#5b9cf6, 80), "Reward", inline="targets", group="Visual")

//-----------------------------------------------------------------------------
// Types
//-----------------------------------------------------------------------------
type SwingPoint
    int barIndex
    float price
    bool isHigh

type ZigZag
    array<int> directions
    array<int> barIndices
    array<float> prices

type UnicornPattern
    // Pattern structure
    bool isActive
    bool isBullish
    bool isTriggered
    bool isEarlyEntry

    // Swing points (A-B-C structure for early entry)
    float pointA
    float pointB
    float pointC
    float pointD
    int barA
    int barB
    int barC
    int barD

    // FVG details
    box fvgBox
    float fvgTop
    float fvgBottom

    // Pattern visualization
    box patternBox
    line topLine
    line bottomLine
    line stopLine

    // Risk management
    float stopLevel
    float targetLevel
    float trailingStop
    bool targetHit

    // Target areas
    box riskBox
    box rewardBox

    // Early entry specific
    bool sweepDetected
    float sweepLevel
    int sweepBar

//-----------------------------------------------------------------------------
// Variables
//-----------------------------------------------------------------------------
var zigzag = ZigZag.new( array.new<int>(100, 0), array.new<int>(100, 0), array.new<float>(100, na))

var swingHighs = array.new<SwingPoint>()
var swingLows = array.new<SwingPoint>()

var bullishPatterns = array.new<UnicornPattern>()
var bearishPatterns = array.new<UnicornPattern>()

var float maxPivot = 0.0
var float minPivot = 999999.0

//-----------------------------------------------------------------------------
// Helper Functions
//-----------------------------------------------------------------------------
updateZigZag(direction, barIdx, price) =>
    zigzag.directions.unshift(direction)
    zigzag.barIndices.unshift(barIdx)
    zigzag.prices.unshift(price)

    if zigzag.directions.size() > 100
        zigzag.directions.pop()
        zigzag.barIndices.pop()
        zigzag.prices.pop()

cleanupPattern(pattern) =>
    if not na(pattern.fvgBox)
        box.delete(pattern.fvgBox)
    if not na(pattern.patternBox)
        box.delete(pattern.patternBox)
    if not na(pattern.topLine)
        line.delete(pattern.topLine)
    if not na(pattern.bottomLine)
        line.delete(pattern.bottomLine)
    if not na(pattern.stopLine)
        line.delete(pattern.stopLine)
    if not na(pattern.riskBox)
        box.delete(pattern.riskBox)
    if not na(pattern.rewardBox)
        box.delete(pattern.rewardBox)

// Early entry detection functions
detectSweep(isBullish, sweepLevel, currentBar) =>
    if isBullish
        // For bullish patterns, detect sweep of recent swing low
        if low <= sweepLevel and close > sweepLevel
            true
        else
            false
    else
        // For bearish patterns, detect sweep of recent swing high
        if high >= sweepLevel and close < sweepLevel
            true
        else
            false

//-----------------------------------------------------------------------------
// Main Logic
//-----------------------------------------------------------------------------
currentBar = bar_index
atrValue = ta.atr(14)

// Detect pivot points
pivotHigh = ta.pivothigh(swingLength, 1)
pivotLow = ta.pivotlow(swingLength, 1)

// Update swing points
if not na(pivotHigh)
    maxPivot := math.max(maxPivot, pivotHigh)

    // Clean old higher lows
    if swingHighs.size() > 0
        for i = swingHighs.size() - 1 to 0
            if swingHighs.get(i).price <= pivotHigh
                swingHighs.remove(i)

    swingHighs.unshift(SwingPoint.new(currentBar - 1, pivotHigh, true))

    // Limit array size
    if swingHighs.size() > 20
        swingHighs.pop()

    // Update zigzag
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection <= 0
        updateZigZag(1, currentBar - 1, pivotHigh)
    else
        if pivotHigh > zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotHigh)

if not na(pivotLow)
    minPivot := math.min(minPivot, pivotLow)

    // Clean old lower highs
    if swingLows.size() > 0
        for i = swingLows.size() - 1 to 0
            if swingLows.get(i).price >= pivotLow
                swingLows.remove(i)

    swingLows.unshift(SwingPoint.new(currentBar - 1, pivotLow, false))

    // Limit array size
    if swingLows.size() > 20
        swingLows.pop()

    // Update zigzag
    lastDirection = zigzag.directions.size() > 0 ? zigzag.directions.get(0) : 0
    if lastDirection >= 0
        updateZigZag(-1, currentBar - 1, pivotLow)
    else
        if pivotLow < zigzag.prices.get(0)
            zigzag.barIndices.set(0, currentBar - 1)
            zigzag.prices.set(0, pivotLow)

// Check for unicorn patterns (A-B-C structure for early entry)
if zigzag.directions.size() >= 3 and zigzag.barIndices.size() >= 3 and zigzag.prices.size() >= 3

    // Get last 3 points (A-B-C structure for early entry)
    pointC = zigzag.prices.get(0)
    pointB = zigzag.prices.get(1)
    pointA = zigzag.prices.get(2)

    barC = zigzag.barIndices.get(0)
    barB = zigzag.barIndices.get(1)
    barA = zigzag.barIndices.get(2)

    dirC = zigzag.directions.get(0)
    dirB = zigzag.directions.get(1)
    dirA = zigzag.directions.get(2)

    // Bullish pattern: Low-High-Low where C < A
    if dirA == -1 and dirB == 1 and dirC == -1 and pointC < pointA and showBull
        // Look for bullish FVG between B and C
        fvgFound = false
        fvgTop = 0.0
        fvgBottom = 0.0
        fvgBar = 0

        // Search for FVG in the B-C range
        searchStart = math.max(0, currentBar - barC)
        searchEnd = math.max(0, currentBar - barB)

        for i = searchStart to math.min(searchEnd, 50)
            if i + 2 < bar_index
                gapBottom = high[i + 2]
                gapTop = low[i]

                if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                    // Check if gap is within pattern range
                    if gapBottom >= pointC and gapBottom <= pointB and gapTop >= pointC and gapTop <= pointB
                        fvgFound := true
                        fvgTop := gapTop
                        fvgBottom := gapBottom
                        fvgBar := currentBar - i - 2
                        break

        if fvgFound
            // Create bullish unicorn pattern (no D)
            newPattern = UnicornPattern.new(
                true, // isActive
                true, // isBullish
                false, // isTriggered
                true, // isEarlyEntry
                pointA, pointB, pointC, na, // D is na
                barA, barB, barC, na, // D is na
                box.new(fvgBar, fvgBottom, currentBar - 1, fvgTop, bgcolor=color.new(bullColor, 75), border_color=color.new(bullColor, 50)),
                fvgTop, fvgBottom,
                box.new(barA, pointA, barC, pointC, bgcolor=color.new(chart.fg_color, 97), border_color=color.new(chart.fg_color, 80)),
                line.new(barA, pointA, currentBar - 1, pointA, color=color.new(chart.fg_color, 50), style=line.style_dashed),
                line.new(barA, pointC, currentBar - 1, pointC, color=color.new(chart.fg_color, 50), style=line.style_dashed),
                line.new(barC, pointC, barC, pointC, color=color.new(chart.fg_color, 60), style=line.style_dotted),
                pointC, pointC + (pointB - pointC) * rrRatio, pointC, false,
                box.new(na, na, na, na), box.new(na, na, na, na),
                false, pointB, na
            )

            bullishPatterns.unshift(newPattern)

            // Clean up if not combining patterns
            if not combinePatterns and bearishPatterns.size() > 0
                for i = bearishPatterns.size() - 1 to 0
                    pattern = bearishPatterns.get(i)
                    if pattern.isActive and not pattern.isTriggered
                        cleanupPattern(pattern)
                        bearishPatterns.remove(i)

    // Bearish pattern: High-Low-High where C > A
    if dirA == 1 and dirB == -1 and dirC == 1 and pointC > pointA and showBear
        // Look for bearish FVG between B and C
        fvgFound = false
        fvgTop = 0.0
        fvgBottom = 0.0
        fvgBar = 0

        // Search for FVG in the B-C range
        searchStart = math.max(0, currentBar - barC)
        searchEnd = math.max(0, currentBar - barB)

        for i = searchStart to math.min(searchEnd, 50)
            if i + 2 < bar_index
                gapTop = low[i + 2]
                gapBottom = high[i]

                if gapBottom < gapTop and (gapTop - gapBottom) > atrValue * 0.05
                    // Check if gap is within pattern range
                    if gapTop <= pointC and gapTop >= pointB and gapBottom <= pointC and gapBottom >= pointB
                        fvgFound := true
                        fvgTop := gapTop
                        fvgBottom := gapBottom
                        fvgBar := currentBar - i - 2
                        break

        if fvgFound
            // Create bearish unicorn pattern (no D)
            newPattern = UnicornPattern.new(
                true, // isActive
                false, // isBullish
                false, // isTriggered
                true, // isEarlyEntry
                pointA, pointB, pointC, na, // D is na
                barA, barB, barC, na, // D is na
                box.new(fvgBar, fvgBottom, currentBar - 1, fvgTop, bgcolor=color.new(bearColor, 75), border_color=color.new(bearColor, 50)),
                fvgTop, fvgBottom,
                box.new(barA, pointA, barC, pointC, bgcolor=color.new(chart.fg_color, 97), border_color=color.new(chart.fg_color, 80)),
                line.new(barA, pointA, currentBar - 1, pointA, color=color.new(chart.fg_color, 50), style=line.style_dashed),
                line.new(barA, pointC, currentBar - 1, pointC, color=color.new(chart.fg_color, 50), style=line.style_dashed),
                line.new(barC, pointC, barC, pointC, color=color.new(chart.fg_color, 60), style=line.style_dotted),
                pointC, pointC - (pointC - pointB) * rrRatio, pointC, false,
                box.new(na, na, na, na), box.new(na, na, na, na),
                false, pointB, na
            )

            bearishPatterns.unshift(newPattern)

            // Clean up if not combining patterns
            if not combinePatterns and bullishPatterns.size() > 0
                for i = bullishPatterns.size() - 1 to 0
                    pattern = bullishPatterns.get(i)
                    if pattern.isActive and not pattern.isTriggered
                        cleanupPattern(pattern)
                        bullishPatterns.remove(i)

//-----------------------------------------------------------------------------
// Pattern Management
//-----------------------------------------------------------------------------

// Manage bullish patterns
if bullishPatterns.size() > 0
    for i = bullishPatterns.size() - 1 to 0
        pattern = bullishPatterns.get(i)

        if pattern.isActive
            // Extend FVG box
            if not na(pattern.fvgBox)
                pattern.fvgBox.set_right(currentBar)
                pattern.fvgBox.set_top(pattern.fvgTop)
                pattern.fvgBox.set_bottom(pattern.fvgBottom)

            // Extend pattern box
            if not na(pattern.patternBox)
                pattern.patternBox.set_right(currentBar)
                pattern.patternBox.set_top(pattern.pointA)
                pattern.patternBox.set_bottom(pattern.pointC)

            // Extend pattern lines
            if not na(pattern.topLine)
                pattern.topLine.set_x2(currentBar)
                pattern.topLine.set_y1(pattern.pointA)
                pattern.topLine.set_y2(pattern.pointA)
            if not na(pattern.bottomLine)
                pattern.bottomLine.set_x2(currentBar)
                pattern.bottomLine.set_y1(pattern.pointC)
                pattern.bottomLine.set_y2(pattern.pointC)

            // Check for stop loss hit
            if low < pattern.stopLevel
                if showTargets and not na(pattern.stopLine)
                    pattern.stopLine.set_x2(currentBar)

                pattern.isActive := false
                cleanupPattern(pattern)
                bullishPatterns.remove(i)
                continue

            // Check for sweep detection (for early entry)
            if pattern.isEarlyEntry and not pattern.sweepDetected
                if detectSweep(true, pattern.sweepLevel, currentBar)
                    pattern.sweepDetected := true
                    pattern.sweepBar := currentBar

            // Check for pattern trigger
            if not pattern.isTriggered
                triggerCondition = false
                if pattern.isEarlyEntry
                    // Early entry trigger: after sweep, close above FVG top
                    triggerCondition := pattern.sweepDetected and close > pattern.fvgTop and open < pattern.fvgTop
                else
                    // Original trigger condition
                    triggerCondition := close > pattern.fvgTop and open < pattern.fvgTop and open > pattern.fvgBottom and math.min(close[1], open[1]) < pattern.fvgTop and math.max(close[1], open[1]) > pattern.fvgBottom

                if triggerCondition
                    // Pattern triggered
                    pattern.isTriggered := true

                    // Add trigger label
                    labelColor = pattern.isEarlyEntry ? earlyEntryColor : bullColor
                    labelText = pattern.isEarlyEntry ? "🟢" : "●"
                    label.new(currentBar, low, text=labelText, color=color.new(na, na), textcolor=labelColor, style=label.style_label_center, size=size.tiny)

                    // Create target areas
                    if showTargets
                        pattern.riskBox := box.new(chart.point.from_index(currentBar, pattern.stopLevel), chart.point.from_index(currentBar + 1, pattern.fvgTop), border_color=color.new(na, na), bgcolor=riskAreaColor)

                        pattern.rewardBox := box.new(chart.point.from_index(currentBar, pattern.fvgTop), chart.point.from_index(currentBar + 1, pattern.targetLevel), border_color=color.new(na, na), bgcolor=rewardAreaColor)

                    // Set trailing stop
                    if enableTrailing
                        pattern.trailingStop := pattern.stopLevel
            else
                // Manage triggered pattern
                if not na(pattern.riskBox)
                    pattern.riskBox.set_right(currentBar + 1)
                if not na(pattern.rewardBox)
                    pattern.rewardBox.set_right(currentBar + 1)

                // Check for target hit
                if high > pattern.targetLevel and not pattern.targetHit
                    pattern.targetHit := true
                    if showTargets
                        line.new(currentBar, pattern.targetLevel, currentBar + 10, pattern.targetLevel, color=color.new(chart.fg_color, 50), style=line.style_solid)

                    pattern.isActive := false
                    cleanupPattern(pattern)
                    bullishPatterns.remove(i)
                    continue

                // Update trailing stop
                if enableTrailing
                    trailingPivot = ta.pivotlow(trailingLength, trailingLength)
                    if not na(trailingPivot)
                        pattern.trailingStop := math.max(pattern.trailingStop, trailingPivot)

// Manage bearish patterns
if bearishPatterns.size() > 0
    for i = bearishPatterns.size() - 1 to 0
        pattern = bearishPatterns.get(i)

        if pattern.isActive
            // Extend FVG box
            if not na(pattern.fvgBox)
                pattern.fvgBox.set_right(currentBar)
                pattern.fvgBox.set_top(pattern.fvgTop)
                pattern.fvgBox.set_bottom(pattern.fvgBottom)

            // Extend pattern box
            if not na(pattern.patternBox)
                pattern.patternBox.set_right(currentBar)
                pattern.patternBox.set_top(pattern.pointA)
                pattern.patternBox.set_bottom(pattern.pointC)

            // Extend pattern lines
            if not na(pattern.topLine)
                pattern.topLine.set_x2(currentBar)
                pattern.topLine.set_y1(pattern.pointA)
                pattern.topLine.set_y2(pattern.pointA)
            if not na(pattern.bottomLine)
                pattern.bottomLine.set_x2(currentBar)
                pattern.bottomLine.set_y1(pattern.pointC)
                pattern.bottomLine.set_y2(pattern.pointC)

            // Check for stop loss hit
            if high > pattern.stopLevel
                if showTargets and not na(pattern.stopLine)
                    pattern.stopLine.set_x2(currentBar)

                pattern.isActive := false
                cleanupPattern(pattern)
                bearishPatterns.remove(i)
                continue

            // Check for sweep detection (for early entry)
            if pattern.isEarlyEntry and not pattern.sweepDetected
                if detectSweep(false, pattern.sweepLevel, currentBar)
                    pattern.sweepDetected := true
                    pattern.sweepBar := currentBar

            // Check for pattern trigger
            if not pattern.isTriggered
                triggerCondition = false
                if pattern.isEarlyEntry
                    // Early entry trigger: after sweep, close below FVG bottom
                    triggerCondition := pattern.sweepDetected and close < pattern.fvgBottom and open > pattern.fvgBottom
                else
                    // Original trigger condition
                    triggerCondition := close < pattern.fvgBottom and open > pattern.fvgBottom and open < pattern.fvgTop and math.max(close[1], open[1]) > pattern.fvgBottom and math.min(close[1], open[1]) < pattern.fvgTop

                if triggerCondition
                    // Pattern triggered
                    pattern.isTriggered := true

                    // Add trigger label
                    labelColor = pattern.isEarlyEntry ? earlyEntryColor : bearColor
                    labelText = pattern.isEarlyEntry ? "🔴" : "●"
                    label.new(currentBar, high, text=labelText, color=color.new(na, na), textcolor=labelColor, style=label.style_label_center, size=size.tiny)

                    // Create target areas
                    if showTargets
                        pattern.riskBox := box.new(chart.point.from_index(currentBar, pattern.fvgBottom), chart.point.from_index(currentBar + 1, pattern.stopLevel), border_color=color.new(na, na), bgcolor=riskAreaColor)

                        pattern.rewardBox := box.new(chart.point.from_index(currentBar, pattern.targetLevel), chart.point.from_index(currentBar + 1, pattern.fvgBottom), border_color=color.new(na, na), bgcolor=rewardAreaColor)

                    // Set trailing stop
                    if enableTrailing
                        pattern.trailingStop := pattern.stopLevel
            else
                // Manage triggered pattern
                if not na(pattern.riskBox)
                    pattern.riskBox.set_right(currentBar + 1)
                if not na(pattern.rewardBox)
                    pattern.rewardBox.set_right(currentBar + 1)

                // Check for target hit
                if low < pattern.targetLevel and not pattern.targetHit
                    pattern.targetHit := true
                    if showTargets
                        line.new(currentBar, pattern.targetLevel, currentBar + 10, pattern.targetLevel, color=color.new(chart.fg_color, 50), style=line.style_solid)

                    pattern.isActive := false
                    cleanupPattern(pattern)
                    bearishPatterns.remove(i)
                    continue

                // Update trailing stop
                if enableTrailing
                    trailingPivot = ta.pivothigh(trailingLength, trailingLength)
                    if not na(trailingPivot)
                        pattern.trailingStop := math.min(pattern.trailingStop, trailingPivot)

// Clean up old patterns and limit number of active patterns for performance
maxPatterns = 10
maxAge = 50  // Maximum age of patterns in bars

// Clean up old bullish patterns
if bullishPatterns.size() > 0
    for i = bullishPatterns.size() - 1 to 0
        pattern = bullishPatterns.get(i)
        if currentBar - pattern.barC > maxAge
            cleanupPattern(pattern)
            bullishPatterns.remove(i)

// Clean up old bearish patterns
if bearishPatterns.size() > 0
    for i = bearishPatterns.size() - 1 to 0
        pattern = bearishPatterns.get(i)
        if currentBar - pattern.barC > maxAge
            cleanupPattern(pattern)
            bearishPatterns.remove(i)

// Limit number of active patterns
if bullishPatterns.size() > maxPatterns
    for i = maxPatterns to bullishPatterns.size() - 1
        cleanupPattern(bullishPatterns.get(i))
        bullishPatterns.remove(i)

if bearishPatterns.size() > maxPatterns
    for i = maxPatterns to bearishPatterns.size() - 1
        cleanupPattern(bearishPatterns.get(i))
        bearishPatterns.remove(i)

//-----------------------------------------------------------------------------
// Plotting and Alerts
//-----------------------------------------------------------------------------

// Plot trailing stops
var float bullTrailing = na
var float bearTrailing = na

if enableTrailing and bullishPatterns.size() > 0
    for pattern in bullishPatterns
        if pattern.isTriggered and pattern.isActive
            bullTrailing := pattern.trailingStop
            break

if enableTrailing and bearishPatterns.size() > 0
    for pattern in bearishPatterns
        if pattern.isTriggered and pattern.isActive
            bearTrailing := pattern.trailingStop
            break

plot(bullTrailing, color=bullColor, style=plot.style_linebr, linewidth=2, title="Bull Trailing Stop")
plot(bearTrailing, color=bearColor, style=plot.style_linebr, linewidth=2, title="Bear Trailing Stop")

// Alerts - using simple trigger detection
var bool prevBullTriggered = false
var bool prevBearTriggered = false
var bool prevEarlyBullTriggered = false
var bool prevEarlyBearTriggered = false

bullishTrigger = false
bearishTrigger = false
earlyBullishTrigger = false
earlyBearishTrigger = false

currentBullTriggered = false
currentBearTriggered = false
currentEarlyBullTriggered = false
currentEarlyBearTriggered = false

if bullishPatterns.size() > 0
    pattern = bullishPatterns.get(0)
    if pattern.isTriggered
        if pattern.isEarlyEntry
            currentEarlyBullTriggered := true
        else
            currentBullTriggered := true

if bearishPatterns.size() > 0
    pattern = bearishPatterns.get(0)
    if pattern.isTriggered
        if pattern.isEarlyEntry
            currentEarlyBearTriggered := true
        else
            currentBearTriggered := true

// Detect new triggers
if currentBullTriggered and not prevBullTriggered
    bullishTrigger := true

if currentBearTriggered and not prevBearTriggered
    bearishTrigger := true

if currentEarlyBullTriggered and not prevEarlyBullTriggered
    earlyBullishTrigger := true

if currentEarlyBearTriggered and not prevEarlyBearTriggered
    earlyBearishTrigger := true

// Update previous state
prevBullTriggered := currentBullTriggered
prevBearTriggered := currentBearTriggered
prevEarlyBullTriggered := currentEarlyBullTriggered
prevEarlyBearTriggered := currentEarlyBearTriggered

if bullishTrigger
    alert("🦄 Bullish Unicorn Pattern Triggered! Entry signal detected.", alert.freq_once_per_bar_close)

if bearishTrigger
    alert("🦄 Bearish Unicorn Pattern Triggered! Entry signal detected.", alert.freq_once_per_bar_close)

if earlyBullishTrigger
    alert("🟢 Early Bullish Unicorn Pattern Triggered! Early entry signal detected.", alert.freq_once_per_bar_close)

if earlyBearishTrigger
    alert("🔴 Early Bearish Unicorn Pattern Triggered! Early entry signal detected.", alert.freq_once_per_bar_close)

// Alert conditions for TradingView alerts
alertcondition(bullishTrigger, "Bullish Unicorn", "🦄 Bullish Unicorn Pattern Triggered")
alertcondition(bearishTrigger, "Bearish Unicorn", "🦄 Bearish Unicorn Pattern Triggered")
alertcondition(earlyBullishTrigger, "Early Bullish Unicorn", "🟢 Early Bullish Unicorn Pattern Triggered")
alertcondition(earlyBearishTrigger, "Early Bearish Unicorn", "🔴 Early Bearish Unicorn Pattern Triggered")
alertcondition(bullishTrigger or bearishTrigger or earlyBullishTrigger or earlyBearishTrigger, "Any Unicorn", "🦄 Unicorn Pattern Triggered")
