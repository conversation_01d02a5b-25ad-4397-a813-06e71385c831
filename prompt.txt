## 📌 Pine Script Prompt: Bidirectional Early Entry Logic for Unicorn v6

Enhance the **Unicorn v6** Pine Script by adding **early entry detection** logic based on the **A–B–C structure** for both **bullish (uptrend)** and **bearish (downtrend)** patterns — **without waiting for point D**. Once point **C** is confirmed:

---

### 🟢 For Bullish Setups (Uptrend)
- Confirm the structure:  
  `A = Low`, `B = High`, `C = Higher Low`
- Identify a **bearish inversion FVG** between points **B and C**
- Wait for price to **sweep the most recent swing low**, then clear the FVG
- If price **closes above the top of the inversion FVG** after the sweep,  
  ➤ **Trigger early bullish entry**
- Mark the entry with a **green label or arrow**
- Optionally **plot a risk/reward box**

---

### 🔴 For Bearish Setups (Downtrend)
- Confirm the structure:  
  `A = High`, `B = Low`, `C = Lower High`
- Identify a **bullish inversion FVG** between points **B and C**
- Wait for price to **sweep the most recent swing high**, then clear the FVG
- If price **closes below the bottom of the inversion FVG** after the sweep,  
  ➤ **Trigger early bearish entry**
- Mark the entry with a **red label or arrow**
- Optionally **plot a risk/reward box**

---

### ⚙️ Additional Requirements
- Add a **toggle input** to enable/disable this early entry logic independently  
  of the original **A–B–C–D pattern logic**
- Keep the **original D-point based entry logic** intact
- Enable **plotting alerts** for both **early bullish** and **bearish entries**
